<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Services\Evm\EvmWalletService;
use App\CurrencyProviders\TronProvider;

class AdminWalletKey extends Model
{
    use HasFactory;
    protected $fillable = [
        "uid",
        "network_id",
        "address",
        "pv",
        "creation_type",
        "status",
    ];

    protected $appends = ['balance'];

    public function network()
    {
        return $this->belongsTo(Network::class, "network_id");
    }

    /**
     * Get wallet balance from blockchain
     */
    public function getBalanceAttribute()
    {
        try {
            if (!$this->attributes['address'] || !$this->network) {
                return '0.00000000';
            }

            // Check network type and get balance accordingly
            switch (strtolower($this->network->base_type ?? '')) {
                case 'tron':
                case 'trx':
                    return $this->getTronBalance();

                case 'evm':
                case 'ethereum':
                case 'bsc':
                case 'polygon':
                    return $this->getEvmBalance();

                default:
                    return '0.00000000';
            }
        } catch (\Exception $e) {
            \Log::error('Error getting wallet balance: ' . $e->getMessage());
            return '0.00000000';
        }
    }

    /**
     * Get Tron network balance
     */
    private function getTronBalance()
    {
        try {
            $tronProvider = new TronProvider();
            $balance = $tronProvider->trxBalance($this->attributes['address']);
            return $balance ?? '0.000000';
        } catch (\Exception $e) {
            \Log::error('Error getting Tron balance: ' . $e->getMessage());
            return '0.000000';
        }
    }

    /**
     * Get EVM network balance
     */
    private function getEvmBalance()
    {
        try {
            $evmService = new EvmWalletService();
            $response = $evmService->getWalletBalance($this->network_id, $this->address);

            if (isset($response['success']) && $response['success'] && isset($response['data']['balance'])) {
                return number_format($response['data']['balance'], 8, '.', '');
            }

            return 0;
        } catch (\Exception $e) {
            \Log::error('Error getting EVM balance: ' . $e->getMessage());
            return 0;
        }
    }
}
