@extends('admin.layouts.app')

@push('styles')
<style>
.balance-badge {
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    min-width: 120px;
    text-align: center;
}

.refresh-balance {
    border: none;
    background: transparent;
    color: #6c757d;
    transition: color 0.2s;
}

.refresh-balance:hover {
    color: #007bff;
}

.table td {
    vertical-align: middle;
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">کیف پول‌های سیستمی</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.system-wallets.create') }}" class="btn btn-primary">
                            ایجاد کیف پول جدید
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>شبکه</th>
                                <th>آدرس</th>
                                <th>موجودی فعلی</th>
                                <th>وضعیت</th>
                                <th>عملیات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($wallets as $wallet)
                            <tr>
                                <td>
                                    @if($wallet->network->logo)
                                        <img src="{{ asset('storage/' . $wallet->network->logo) }}"
                                             alt="{{ $wallet->network->name }}"
                                             height="40">
                                    @endif
                                    {{ $wallet->network->name }}
                                </td>
                                <td>
                                    <span class="text-muted small">{{ $wallet->address }}</span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="badge badge-primary balance-badge"
                                              data-wallet-id="{{ $wallet->id }}">
                                            <i class="fas fa-spinner fa-spin" id="loading-{{ $wallet->id }}"></i>
                                            <span id="balance-{{ $wallet->id }}" style="display: none;">
                                                {{ $wallet->balance ?? '0.00000000' }}
                                            </span>
                                        </span>
                                        <button class="btn btn-sm btn-outline-secondary ml-2 refresh-balance"
                                                data-wallet-id="{{ $wallet->id }}"
                                                title="بروزرسانی موجودی">
                                            <i class="fas fa-sync-alt"></i>
                                        </button>
                                    </div>
                                </td>
                                <td>
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox"
                                               class="custom-control-input status-toggle"
                                               id="status_{{ $wallet->id }}"
                                               data-id="{{ $wallet->id }}"
                                               {{ $wallet->status ? 'checked' : '' }}>
                                        <label class="custom-control-label"
                                               for="status_{{ $wallet->id }}"></label>
                                    </div>
                                </td>
                                <td>
                                    <a href="{{ route('admin.system-wallets.edit', $wallet->id) }}"
                                       class="btn btn-sm btn-info">
                                        ویرایش
                                    </a>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                    {{ $wallets->links() }}
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Load balances on page load
    loadAllBalances();

    $('.status-toggle').on('change', function() {
        const id = $(this).data('id');

        $.ajax({
            url: '{{ route("admin.system-wallets.toggle-status") }}',
            type: 'POST',
            data: {
                id: id,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                } else {
                    toastr.error(response.message);
                }
            }
        });
    });

    // Refresh balance button
    $('.refresh-balance').on('click', function() {
        const walletId = $(this).data('wallet-id');
        loadBalance(walletId);
    });

    function loadAllBalances() {
        $('.balance-badge').each(function() {
            const walletId = $(this).data('wallet-id');
            loadBalance(walletId);
        });
    }

    function loadBalance(walletId) {
        const loadingElement = $('#loading-' + walletId);
        const balanceElement = $('#balance-' + walletId);

        // Show loading
        loadingElement.show();
        balanceElement.hide();

        $.ajax({
            url: '{{ route("admin.system-wallets.get-balance") }}',
            type: 'POST',
            data: {
                wallet_id: walletId,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    balanceElement.text(response.balance);
                } else {
                    balanceElement.text('خطا');
                    console.error('Error loading balance:', response.message);
                }
            },
            error: function() {
                balanceElement.text('خطا');
                console.error('Error loading balance for wallet:', walletId);
            },
            complete: function() {
                // Hide loading and show balance
                loadingElement.hide();
                balanceElement.show();
            }
        });
    }
});
</script>
@endpush